apiVersion: apps/v1
kind: Deployment
metadata:
  name: $PROJECT_NAME_SANITIZED
  labels:
    app: $PROJECT_NAME_SANITIZED
  namespace: $NAMESPACE
spec:
  replicas: 2
  selector:
    matchLabels:
      app: $PROJECT_NAME_SANITIZED
  template:
    metadata:
      labels:
        app: $PROJECT_NAME_SANITIZED
    spec:
      containers:
        - name: $PROJECT_NAME_SANITIZED
          image: $FULL_IMAGE
          ports:
            - containerPort: 1337
          env:
            - name: NODE_ENV
              value: $NODE_ENV
            - name: MODE
              value: $MODE