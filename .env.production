# Server
# HOST=0.0.0.0
# PORT=1337

# Secrets
APP_KEYS=W3QiRBB1UddCZez7I26qhA==,nd572Df21Y/Xd30Bc5loUA==,mAAxAk732Sanp50OmxzbUA==,2e5Pc+laPrqtlVMdUEVgYw==
API_TOKEN_SALT=mU2xgN3NoCltahCM0nXPMw==
ADMIN_JWT_SECRET=MACsxHF6hvkbo7lirZF9vQ==
TRANSFER_TOKEN_SALT=v2jNAqV1TxwrX5O8obS4yg==

# @strapi-community/dockerize variables
DATABASE_HOST=*************
DATABASE_PORT=5000
DATABASE_NAME=strapi
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=HNoMphznkckftZ5H24oxddBCNULaCXhO
NODE_ENV=production
DATABASE_CLIENT=postgres
DATABASE_SSL=true
# @strapi-community/dockerize end variables
JWT_SECRET=nGE6ceGxpx/BTe/hwuKrYg==

