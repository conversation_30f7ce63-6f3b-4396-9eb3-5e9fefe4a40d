/**
 * banner controllers
 */

import { factories } from '@strapi/strapi'
import {Context} from 'koa'

export default factories.createCoreController('api::banner.banner', {
    async findByType(ctx: Context) {
        const { type } = ctx.params;

        const banners = await strapi.service('api::banner.banner').find({
            where: { type },
        });

        if (!banners || banners?.length === 0) {
            return ctx.notFound('No banners found for this type');
        }

        return banners;
    },
});
