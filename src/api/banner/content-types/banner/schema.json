{"kind": "collectionType", "collectionName": "banners", "info": {"singularName": "banner", "pluralName": "banners", "displayName": "Banners", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "isActive": {"type": "boolean"}, "endDate": {"type": "datetime", "required": true}, "startDate": {"type": "datetime", "required": true}, "description": {"type": "string"}, "banner_types": {"type": "relation", "relation": "oneToOne", "target": "api::banner-type.banner-type"}}}