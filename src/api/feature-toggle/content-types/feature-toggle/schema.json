{"kind": "collectionType", "collectionName": "feature_toggles", "info": {"singularName": "feature-toggle", "pluralName": "feature-toggles", "displayName": "Feature Toggles", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"flag": {"type": "boolean", "default": false, "required": true}, "description": {"type": "text", "required": true}, "name": {"type": "string", "unique": true, "required": true}}}