{"kind": "collectionType", "collectionName": "landing_pages", "info": {"singularName": "landing-page", "pluralName": "landing-pages", "displayName": "Landing Pages", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "data": {"type": "json", "required": true}, "isActive": {"type": "boolean", "default": false}, "description": {"type": "text"}, "endDate": {"type": "datetime", "required": true}, "slug": {"type": "string", "required": true, "unique": true}}}