/**
 * landing-page controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::landing-page.landing-page', ({ strapi }) => ({
  async find(ctx) {
    const now = new Date();
    
    const allLandingPages = await strapi.entityService.findMany('api::landing-page.landing-page', {
      filters: ctx.query.filters || {},
      populate: ctx.query.populate || {},
      sort: ctx.query.sort || {},
      pagination: ctx.query.pagination || {}
    });
    
    for (const landingPage of allLandingPages) {
      const endDate = new Date(landingPage.endDate);
      if (endDate < now && landingPage.isActive) {
        await strapi.entityService.update('api::landing-page.landing-page', landingPage.id, {
          data: { isActive: false }
        });
      }
    }
    
    return { data: allLandingPages };
  },

  async create(ctx) {
    const { data } = ctx.request.body;

    const landingPage = await strapi.entityService.create('api::landing-page.landing-page', {
      data: data
    });

    return landingPage;
  },

  async findOne(ctx) {
    const { id } = ctx.params;
    let landingPage = null;

    // Eğer id integer ise id ile, değilse slug ile ara
    if (/^\d+$/.test(id)) {
      landingPage = await strapi.entityService.findOne('api::landing-page.landing-page', id);
    } else {
      const results = await strapi.entityService.findMany('api::landing-page.landing-page', {
        filters: { slug: id },
        limit: 1,
      });
      landingPage = results && results.length > 0 ? results[0] : null;
    }
    
    if (!landingPage) {
      return ctx.notFound('Landing page not found');
    }

    const now = new Date();
    const endDate = new Date(landingPage.endDate);

    if (endDate < now) {
      await strapi.entityService.update('api::landing-page.landing-page', landingPage.id, {
        data: { isActive: false }
      });
      landingPage.isActive = false;
    }

    return landingPage;
  },

  async update(ctx) {
    const { id } = ctx.params;
    const { data } = ctx.request.body;

    const landingPage = await strapi.entityService.findOne('api::landing-page.landing-page', id);
    
    if (!landingPage) {
      return ctx.notFound('Landing page not found');
    }

    const updatedLandingPage = await strapi.entityService.update('api::landing-page.landing-page', id, {
      data: data
    });

    return updatedLandingPage;
  },

  async delete(ctx) {
    const { id } = ctx.params;

    const landingPage = await strapi.entityService.findOne('api::landing-page.landing-page', id);
    
    if (!landingPage) {
      return ctx.notFound('Landing page not found');
    }

    await strapi.entityService.delete('api::landing-page.landing-page', id);

    return { message: 'Landing page deleted successfully' };
  }
}));
