{"kind": "collectionType", "collectionName": "dynamic_components", "info": {"singularName": "dynamic-component", "pluralName": "dynamic-components", "displayName": "Dynamic Components", "description": "Yönetilebilir, sırası değiştirilebilir component yapısı"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"type": {"type": "string", "required": true}, "order": {"type": "integer", "required": true, "default": 1}, "componentId": {"type": "uid", "targetField": "type"}, "data": {"type": "json", "required": true}, "isActive": {"type": "boolean", "default": true}, "platform": {"type": "enumeration", "enum": ["mobile", "web", "both"], "default": "both"}, "page": {"type": "string", "default": "home"}}}