export default ({env}) => ({
    connection: {
        client: 'postgres',
        connection: {
            host: env('DATABASE_HOST', 'localhost'),
            port: env.int('DATABASE_PORT', 5432),
            database: env('DATABASE_NAME', 'strapi'),
            user: env('DATABASE_USERNAME', 'strapi'),
            password: env('DATABASE_PASSWORD', 'password'),
            ssl: env.bool('DATABASE_SSL', false)
                ? {rejectUnauthorized: env.bool('DATABASE_SSL_REJECT_UNAUTHORIZED', false)}
                : false,
        },
    },
});

